import 'dart:convert';

/// Model class representing a campaign from the Supabase 'campaigns' table
class Campaign {
  final String id; // Changed from int to String for UUID
  final String name;
  final DateTime startDate;
  final DateTime endDate;
  final String status;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? metadata;

  Campaign({
    required this.id,
    required this.name,
    required this.startDate,
    required this.endDate,
    required this.status,
    this.createdAt,
    this.updatedAt,
    this.metadata,
  });

  /// Create a Campaign from a JSON map
  factory Campaign.fromJson(Map<String, dynamic> json) {
    return Campaign(
      id: json['id']?.toString() ?? '', // Ensure id is a string
      name: json['name'] ?? '',
      startDate: json['start_date'] != null
          ? DateTime.parse(json['start_date'])
          : DateTime.now(),
      endDate: json['end_date'] != null
          ? DateTime.parse(json['end_date'])
          : DateTime.now().add(const Duration(days: 1)),
      status: json['status'] ?? 'active',
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
      metadata: json['metadata'] != null
          ? (json['metadata'] is String
              ? jsonDecode(json['metadata'])
              : json['metadata'])
          : null,
    );
  }

  /// Convert Campaign to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'status': status,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'metadata': metadata != null
          ? (metadata is String ? metadata : jsonEncode(metadata))
          : null,
    };
  }

  @override
  String toString() {
    return 'Campaign(id: $id, name: $name, status: $status)';
  }
}
