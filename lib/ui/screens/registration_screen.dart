import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:signage/core/models/settings.dart';
import 'package:signage/core/services/supabase_service.dart';
import 'package:signage/core/services/logging_service.dart';
import 'package:signage/ui/screens/data_loading_screen.dart';
import 'package:signage/utils/fullscreen_utils.dart';

class RegistrationScreen extends StatefulWidget {
  const RegistrationScreen({super.key});

  @override
  State<RegistrationScreen> createState() => _RegistrationScreenState();
}

class _RegistrationScreenState extends State<RegistrationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _codeController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    FullscreenUtils.ensureFullscreen();
  }

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  /// Validate the registration code
  String? _validateCode(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter a registration code';
    }

    if (value.length != 5) {
      return 'Registration code must be 5 digits';
    }

    if (!RegExp(r'^\d{5}$').hasMatch(value)) {
      return 'Registration code must contain only digits';
    }

    return null;
  }

  /// Register the device with the given code
  Future<void> _register() async {
    // Validate form
    if (_formKey.currentState?.validate() != true) {
      return;
    }

    // Hide keyboard
    FocusScope.of(context).unfocus();

    // Show loading indicator
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Get the code as a string
      final String codeString = _codeController.text.toString();

      // Fetch screen info by code
      final screen = await SupabaseService.fetchScreenByCode(codeString);

      if (screen == null) {
        // Invalid code or screen already registered
        setState(() {
          _isLoading = false;
          _errorMessage = 'Invalid code or screen already registered';
          _codeController.clear();
        });
        return;
      }

      // Initialize logging service with screen ID and log registration start
      final loggingService = LoggingService();
      loggingService.initialize(screen.id);
      loggingService.logRegistrationStart();

      // Update screen registration status
      final updateResult = await SupabaseService.updateScreenRegistration(screen.id);
      if (!updateResult) {
        // Log error
        loggingService.logError('Failed to update screen registration status');
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to update screen registration status';
        });
        return;
      }

      // Register player
      final registerResult = await SupabaseService.registerPlayer(screen.id);
      if (!registerResult) {
        // Log error
        loggingService.logError('Failed to register player');
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to register player';
        });
        return;
      }

      // Create settings from screen
      final settings = Settings.fromScreen(screen);

      // Save settings
      await settings.save();

      // Log successful registration
      loggingService.logRegistrationSuccess();

      // Navigate to data loading screen
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const DataLoadingScreen(),
          ),
        );
      }
    } catch (e) {
      // Log error if we have a screen ID available
      try {
        final settings = await Settings.load();
        if (settings != null) {
          final loggingService = LoggingService();
          loggingService.initialize(settings.screenId);
          loggingService.logError('Registration failed: $e');
        }
      } catch (logError) {
        // If we can't log the error, just print it
        debugPrint('Failed to log registration error: $logError');
      }

      setState(() {
        _isLoading = false;
        _errorMessage = 'Registration failed: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: null, // Remove app bar
      extendBodyBehindAppBar: true, // Extend body behind app bar
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Logo
                Image.asset(
                  'assets/images/logo.png',
                  width: 150,
                  height: 150,
                ),
                const SizedBox(height: 20),

                // Header text
                const Text(
                  'Signage Player',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 40),

                // Registration code input
                TextFormField(
                  controller: _codeController,
                  decoration: const InputDecoration(
                    labelText: 'Registration Code',
                    hintText: 'Enter 5-digit code',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.vpn_key),
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(5),
                  ],
                  validator: _validateCode,
                  enabled: !_isLoading,
                ),
                const SizedBox(height: 20),

                // Error message
                if (_errorMessage != null)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 20),
                    child: Text(
                      _errorMessage!,
                      style: const TextStyle(
                        color: Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                // Register button
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _register,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                    child: _isLoading
                        ? const CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          )
                        : const Text(
                            'Register',
                            style: TextStyle(fontSize: 18),
                          ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
